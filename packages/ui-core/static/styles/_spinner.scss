/* CSS Variables for Skeleton Colors */
:root {
  --skeleton-bg: #202023;
  --skeleton-sidebar-bg: #26262e;
  --skeleton-card-bg: #2a2a32;
  --skeleton-element-bg: #3a3a42;
  --skeleton-shimmer-start: #3a3a42;
  --skeleton-shimmer-mid: #4a4a52;
  --skeleton-shimmer-end: #3a3a42;
  --skeleton-text-primary: #e2e8f0;
  --skeleton-text-secondary: #94a3b8;
  --skeleton-border: #374151;
  --skeleton-active: #6366f1;
  --skeleton-sidebar-width: 260px;
  --z-index-overlay: 1003;
}

/* Skeleton Layout */
.skeleton-layout {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--z-index-overlay);
  display: flex;
  flex-direction: column;
  background: var(--skeleton-bg);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

/* De<PERSON> Banner */
.skeleton-demo-banner {
  width: 100%;
  height: 40px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0 16px;
  flex-shrink: 0;
}

.skeleton-demo-text {
  height: 14px;
  width: 280px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-demo-link {
  height: 14px;
  width: 120px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.2s;
}

/* Main Layout Container */
.skeleton-layout .skeleton-content-wrapper {
  display: flex;
  flex: 1;
  min-height: 0;
}

/* Sidebar */
.skeleton-sidebar {
  width: var(--skeleton-sidebar-width);
  background: var(--skeleton-sidebar-bg);
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--skeleton-border);
  position: relative;
}

/* Logo Section */
.skeleton-logo-section {
  padding: 20px 16px;
  border-bottom: 1px solid var(--skeleton-border);
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeleton-logo {
  width: 32px;
  height: 32px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-logo-text {
  height: 20px;
  width: 80px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.1s;
}

/* Navigation Menu */
.skeleton-nav-menu {
  flex: 1;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skeleton-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin: 0 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.skeleton-nav-active {
  background: var(--skeleton-active);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.skeleton-nav-icon {
  width: 20px;
  height: 20px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-nav-active .skeleton-nav-icon {
  background: rgba(255, 255, 255, 0.2);
}

.skeleton-nav-text {
  height: 16px;
  width: 120px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.1s;
}

.skeleton-nav-active .skeleton-nav-text {
  background: rgba(255, 255, 255, 0.2);
}

/* User Section at Bottom */
.skeleton-user-section {
  padding: 16px;
  border-top: 1px solid var(--skeleton-border);
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: auto;
}

.skeleton-user-avatar {
  width: 40px;
  height: 40px;
  background: var(--skeleton-element-bg);
  border-radius: 50%;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skeleton-user-name {
  height: 14px;
  width: 100px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.1s;
}

.skeleton-user-role {
  height: 12px;
  width: 70px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.2s;
}

/* Main Content Area */
.skeleton-main {
  flex: 1 1 auto;
  background: var(--skeleton-bg);
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header Section */
.skeleton-header {
  height: 64px;
  background: var(--skeleton-sidebar-bg);
  border-bottom: 1px solid var(--skeleton-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  flex-shrink: 0;
}

.skeleton-header-left {
  display: flex;
  align-items: center;
}

.skeleton-breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
}

.skeleton-breadcrumb-item {
  height: 16px;
  width: 80px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-breadcrumb-separator {
  width: 4px;
  height: 4px;
  background: var(--skeleton-element-bg);
  border-radius: 50%;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.1s;
}

.skeleton-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeleton-header-button {
  height: 32px;
  width: 80px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-header-icons {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.skeleton-header-icon {
  width: 24px;
  height: 24px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

/* Page Title Section */
.skeleton-page-title {
  padding: 24px;
  border-bottom: 1px solid var(--skeleton-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.skeleton-title-main {
  height: 28px;
  width: 300px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-title-subtitle {
  height: 16px;
  width: 200px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  margin-top: 8px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.1s;
}

.skeleton-title-actions {
  display: flex;
  gap: 12px;
}

.skeleton-title-button {
  height: 36px;
  width: 100px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

/* Stats Cards Row */
.skeleton-stats-row {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  padding: 24px;
  flex-shrink: 0;
}

.skeleton-stat-card {
  background: var(--skeleton-card-bg);
  border: 1px solid var(--skeleton-border);
  border-radius: 12px;
  padding: 20px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
  overflow: hidden;
}

.skeleton-stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.skeleton-stat-title {
  height: 14px;
  width: 80px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-stat-number {
  height: 32px;
  width: 60px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.1s;
}

.skeleton-stat-progress {
  height: 8px;
  width: 100%;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  margin-top: auto;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.2s;
}

/* Content Grid */
.skeleton-content-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  padding: 24px;
  overflow: auto;
}

.skeleton-left-column,
.skeleton-right-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Section Containers */
.skeleton-section {
  background: var(--skeleton-card-bg);
  border: 1px solid var(--skeleton-border);
  border-radius: 12px;
  overflow: hidden;
}

.skeleton-section-header {
  padding: 20px;
  border-bottom: 1px solid var(--skeleton-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.skeleton-section-title {
  height: 18px;
  width: 120px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-section-action {
  height: 16px;
  width: 60px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.1s;
}

/* Activities Section */
.skeleton-activities {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--skeleton-bg);
  border-radius: 8px;
}

.skeleton-activity-avatar {
  width: 32px;
  height: 32px;
  background: var(--skeleton-element-bg);
  border-radius: 50%;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skeleton-activity-name {
  height: 14px;
  width: 120px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-activity-time {
  height: 12px;
  width: 80px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.1s;
}

.skeleton-activity-screenshots {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.skeleton-screenshot {
  width: 80px;
  height: 60px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-activity-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.skeleton-activity-detail {
  height: 12px;
  width: 150px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

/* Manual Timer Section */
.skeleton-manual-timer {
  padding: 20px;
}

.skeleton-timer-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: var(--skeleton-bg);
  border-radius: 8px;
}

.skeleton-timer-label {
  height: 14px;
  width: 100px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-timer-value {
  height: 14px;
  width: 60px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.1s;
}

/* Tasks Section */
.skeleton-task-item {
  padding: 20px;
}

.skeleton-task-content {
  padding: 16px;
  background: var(--skeleton-bg);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-task-title {
  height: 16px;
  width: 200px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

.skeleton-task-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeleton-task-progress-bar {
  flex: 1;
  height: 8px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.1s;
}

.skeleton-task-progress-text {
  height: 12px;
  width: 40px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite 0.2s;
}

/* Empty State */
.skeleton-empty-state {
  padding: 40px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.skeleton-empty-text {
  height: 14px;
  width: 180px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

/* Members Table */
.skeleton-members-table {
  padding: 20px;
}

.skeleton-table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  padding: 12px;
  background: var(--skeleton-bg);
  border-radius: 8px;
}

.skeleton-table-header-cell {
  height: 14px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: skeleton-shimmer 2s ease-in-out infinite;
}

/* Shimmer Animation */
@keyframes skeleton-shimmer {
  0% {
    background-position: -200px 0;
    background-image: linear-gradient(
      90deg,
      var(--skeleton-shimmer-start) 0%,
      var(--skeleton-shimmer-mid) 50%,
      var(--skeleton-shimmer-end) 100%
    );
  }
  50% {
    background-position: 200px 0;
    background-image: linear-gradient(
      90deg,
      var(--skeleton-shimmer-start) 0%,
      var(--skeleton-shimmer-mid) 50%,
      var(--skeleton-shimmer-end) 100%
    );
  }
  100% {
    background-position: 400px 0;
    background-image: linear-gradient(
      90deg,
      var(--skeleton-shimmer-start) 0%,
      var(--skeleton-shimmer-mid) 50%,
      var(--skeleton-shimmer-end) 100%
    );
  }
}

/* Staggered Animation Delays */
.skeleton-delay-1 {
  animation-delay: 0.1s;
}

.skeleton-delay-2 {
  animation-delay: 0.2s;
}

.skeleton-delay-3 {
  animation-delay: 0.3s;
}

.skeleton-delay-4 {
  animation-delay: 0.4s;
}

.skeleton-delay-5 {
  animation-delay: 0.5s;
}

.skeleton-delay-6 {
  animation-delay: 0.6s;
}

/* Enhanced Shimmer Effect for All Elements */
.skeleton-layout [class*='skeleton-'] {
  background-size: 400px 100%;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

.skeleton-layout
  [class*='skeleton-']:not(.skeleton-layout):not(.skeleton-sidebar):not(.skeleton-main):not(.skeleton-content-grid):not(
    .skeleton-left-column
  ):not(.skeleton-right-column):not(.skeleton-section):not(.skeleton-activities):not(.skeleton-activity-item):not(
    .skeleton-activity-content
  ):not(.skeleton-activity-screenshots):not(.skeleton-activity-details):not(.skeleton-manual-timer):not(
    .skeleton-timer-row
  ):not(.skeleton-task-item):not(.skeleton-task-content):not(.skeleton-task-progress):not(.skeleton-empty-state):not(
    .skeleton-members-table
  ):not(.skeleton-table-header):not(.skeleton-header):not(.skeleton-header-left):not(.skeleton-header-right):not(
    .skeleton-header-icons
  ):not(.skeleton-page-title):not(.skeleton-title-actions):not(.skeleton-stats-row):not(.skeleton-stat-card):not(
    .skeleton-stat-header
  ):not(.skeleton-section-header):not(.skeleton-breadcrumb):not(.skeleton-logo-section):not(.skeleton-nav-menu):not(
    .skeleton-nav-item
  ):not(.skeleton-user-section):not(.skeleton-user-info):not(.skeleton-demo-banner) {
  background-image: linear-gradient(
    90deg,
    var(--skeleton-shimmer-start) 0%,
    var(--skeleton-shimmer-mid) 50%,
    var(--skeleton-shimmer-end) 100%
  );
}

/* Responsive Design */
@media (max-width: 1200px) {
  .skeleton-stats-row {
    grid-template-columns: repeat(3, 1fr);
  }

  .skeleton-content-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .skeleton-layout {
    flex-direction: column;
  }

  .skeleton-sidebar {
    width: 100%;
    height: 60px;
    flex-direction: row;
    align-items: center;
    padding: 0 16px;
  }

  .skeleton-nav-menu {
    display: none;
  }

  .skeleton-user-section {
    margin-top: 0;
    margin-left: auto;
    border-top: none;
    padding: 0;
  }

  .skeleton-logo-section {
    border-bottom: none;
    padding: 0;
  }

  .skeleton-stats-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 16px;
  }

  .skeleton-content-grid {
    padding: 16px;
    gap: 16px;
  }

  .skeleton-header {
    padding: 0 16px;
  }

  .skeleton-page-title {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .skeleton-title-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .skeleton-stats-row {
    grid-template-columns: 1fr;
  }

  .skeleton-header-right {
    gap: 8px;
  }

  .skeleton-header-button {
    width: 60px;
  }

  .skeleton-activity-screenshots {
    flex-wrap: wrap;
  }

  .skeleton-screenshot {
    width: 60px;
    height: 45px;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  :root {
    --skeleton-bg: #1a1a1a;
    --skeleton-sidebar-bg: #2d2d2d;
    --skeleton-card-bg: #2a2a2a;
    --skeleton-element-bg: #3a3a3a;
    --skeleton-shimmer-start: #3a3a3a;
    --skeleton-shimmer-mid: #4a4a4a;
    --skeleton-shimmer-end: #3a3a3a;
    --skeleton-border: #404040;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --skeleton-element-bg: #666666;
    --skeleton-shimmer-start: #666666;
    --skeleton-shimmer-mid: #888888;
    --skeleton-shimmer-end: #666666;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .skeleton-layout [class*='skeleton-'] {
    animation: none;
  }

  .skeleton-layout [class*='skeleton-']:not(.skeleton-layout):not(.skeleton-sidebar):not(.skeleton-main) {
    background-image: none;
    background-color: var(--skeleton-element-bg);
  }
}
