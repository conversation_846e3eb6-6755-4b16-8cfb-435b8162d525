/* Skeleton Loading System */
:root {
  --skeleton-bg: #202023;
  --skeleton-sidebar-bg: #26262e;
  --skeleton-card-bg: #2a2a32;
  --skeleton-element-bg: #3a3a42;
  --skeleton-border: #374151;
  --skeleton-active: #6366f1;
  --skeleton-sidebar-width: 260px;
}

/* Main Layout */
.skeleton-layout {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1003;
  display: flex;
  flex-direction: column;
  background: var(--skeleton-bg);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Demo Banner */
.skeleton-demo-banner {
  height: 40px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.skeleton-demo-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.skeleton-demo-text {
  height: 14px;
  width: 280px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-demo-link {
  height: 14px;
  width: 120px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite 0.2s;
}

/* Content Wrapper */
.skeleton-content-wrapper {
  display: flex;
  flex: 1;
  min-height: 0;
}

/* Sidebar */
.skeleton-sidebar {
  width: var(--skeleton-sidebar-width);
  background: var(--skeleton-sidebar-bg);
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--skeleton-border);
}

/* Logo Section */
.skeleton-logo-section {
  padding: 20px 16px;
  border-bottom: 1px solid var(--skeleton-border);
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeleton-logo {
  width: 32px;
  height: 32px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-logo-text {
  height: 20px;
  width: 80px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite 0.1s;
}

/* Navigation Menu */
.skeleton-nav-menu {
  flex: 1;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skeleton-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin: 0 8px;
  border-radius: 8px;
}

.skeleton-nav-item.active {
  background: var(--skeleton-active);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.skeleton-nav-icon {
  width: 20px;
  height: 20px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-nav-item.active .skeleton-nav-icon {
  background: rgba(255, 255, 255, 0.2);
}

.skeleton-nav-text {
  height: 16px;
  width: 120px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite 0.1s;
}

.skeleton-nav-item.active .skeleton-nav-text {
  background: rgba(255, 255, 255, 0.2);
}

/* User Section */
.skeleton-user-section {
  padding: 16px;
  border-top: 1px solid var(--skeleton-border);
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: auto;
}

.skeleton-user-avatar {
  width: 40px;
  height: 40px;
  background: var(--skeleton-element-bg);
  border-radius: 50%;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skeleton-user-name {
  height: 14px;
  width: 100px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite 0.1s;
}

.skeleton-user-role {
  height: 12px;
  width: 70px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite 0.2s;
}

/* Main Content */
.skeleton-main {
  flex: 1;
  background: var(--skeleton-bg);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header */
.skeleton-header {
  height: 64px;
  background: var(--skeleton-sidebar-bg);
  border-bottom: 1px solid var(--skeleton-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  flex-shrink: 0;
}

.skeleton-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.skeleton-breadcrumb-item {
  height: 16px;
  width: 80px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.skeleton-header-button {
  height: 32px;
  width: 80px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-header-icon {
  width: 24px;
  height: 24px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
}

/* Page Title */
.skeleton-page-title {
  padding: 24px;
  border-bottom: 1px solid var(--skeleton-border);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-shrink: 0;
}

.skeleton-title-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-title-main {
  height: 28px;
  width: 300px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-title-subtitle {
  height: 16px;
  width: 200px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite 0.1s;
}

.skeleton-title-actions {
  display: flex;
  gap: 12px;
}

.skeleton-title-button {
  height: 36px;
  width: 100px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: shimmer 1.5s ease-in-out infinite;
}

/* Stats Cards */
.skeleton-stats-row {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  padding: 24px;
  flex-shrink: 0;
}

.skeleton-stat-card {
  background: var(--skeleton-card-bg);
  border: 1px solid var(--skeleton-border);
  border-radius: 12px;
  padding: 20px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-stat-title {
  height: 14px;
  width: 80px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-stat-number {
  height: 32px;
  width: 60px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: shimmer 1.5s ease-in-out infinite 0.1s;
}

.skeleton-stat-progress {
  height: 8px;
  width: 100%;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  margin-top: auto;
  animation: shimmer 1.5s ease-in-out infinite 0.2s;
}

/* Content Grid */
.skeleton-content-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  padding: 24px;
  overflow: auto;
}

.skeleton-left-column,
.skeleton-right-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Sections */
.skeleton-section {
  background: var(--skeleton-card-bg);
  border: 1px solid var(--skeleton-border);
  border-radius: 12px;
  overflow: hidden;
}

.skeleton-section-header {
  padding: 20px;
  border-bottom: 1px solid var(--skeleton-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.skeleton-section-title {
  height: 18px;
  width: 120px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-section-action {
  height: 16px;
  width: 60px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite 0.1s;
}

.skeleton-section-content {
  padding: 20px;
}

/* Activity Item */
.skeleton-activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--skeleton-bg);
  border-radius: 8px;
  margin-bottom: 16px;
}

.skeleton-activity-avatar {
  width: 32px;
  height: 32px;
  background: var(--skeleton-element-bg);
  border-radius: 50%;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skeleton-activity-name {
  height: 14px;
  width: 120px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-activity-time {
  height: 12px;
  width: 80px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite 0.1s;
}

/* Screenshots */
.skeleton-screenshots {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.skeleton-screenshot {
  width: 80px;
  height: 60px;
  background: var(--skeleton-element-bg);
  border-radius: 6px;
  animation: shimmer 1.5s ease-in-out infinite;
}

/* Activity Details */
.skeleton-activity-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-activity-detail {
  height: 12px;
  width: 150px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
}

/* Timer Row */
.skeleton-timer-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: var(--skeleton-bg);
  border-radius: 8px;
}

.skeleton-timer-label {
  height: 14px;
  width: 100px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-timer-value {
  height: 14px;
  width: 60px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite 0.1s;
}

/* Task Item */
.skeleton-task-item {
  padding: 16px;
  background: var(--skeleton-bg);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-task-title {
  height: 16px;
  width: 200px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
}

.skeleton-task-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.skeleton-task-progress-bar {
  flex: 1;
  height: 8px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite 0.1s;
}

.skeleton-task-progress-text {
  height: 12px;
  width: 40px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite 0.2s;
}

/* Empty State */
.skeleton-empty-text {
  height: 14px;
  width: 180px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
  margin: 20px auto;
  text-align: center;
}

/* Table */
.skeleton-table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  padding: 12px;
  background: var(--skeleton-bg);
  border-radius: 8px;
}

.skeleton-table-cell {
  height: 14px;
  background: var(--skeleton-element-bg);
  border-radius: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
}

/* Shimmer Animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Apply shimmer to all skeleton elements */
.skeleton-layout
  [class*='skeleton-']:not(.skeleton-layout):not(.skeleton-content-wrapper):not(.skeleton-sidebar):not(
    .skeleton-main
  ):not(.skeleton-content-grid):not(.skeleton-left-column):not(.skeleton-right-column):not(.skeleton-section):not(
    .skeleton-section-content
  ):not(.skeleton-activity-item):not(.skeleton-activity-info):not(.skeleton-screenshots):not(
    .skeleton-activity-details
  ):not(.skeleton-timer-row):not(.skeleton-task-item):not(.skeleton-task-progress):not(.skeleton-table-header):not(
    .skeleton-header
  ):not(.skeleton-header-left):not(.skeleton-header-right):not(.skeleton-page-title):not(.skeleton-title-left):not(
    .skeleton-title-actions
  ):not(.skeleton-stats-row):not(.skeleton-stat-card):not(.skeleton-section-header):not(.skeleton-logo-section):not(
    .skeleton-nav-menu
  ):not(.skeleton-nav-item):not(.skeleton-user-section):not(.skeleton-user-info):not(.skeleton-demo-banner):not(
    .skeleton-demo-content
  ) {
  background: linear-gradient(90deg, var(--skeleton-element-bg) 25%, #4a4a52 50%, var(--skeleton-element-bg) 75%);
  background-size: 200px 100%;
  background-repeat: no-repeat;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .skeleton-stats-row {
    grid-template-columns: repeat(3, 1fr);
  }

  .skeleton-content-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .skeleton-content-wrapper {
    flex-direction: column;
  }

  .skeleton-sidebar {
    width: 100%;
    height: 60px;
    flex-direction: row;
    align-items: center;
    padding: 0 16px;
  }

  .skeleton-nav-menu {
    display: none;
  }

  .skeleton-user-section {
    margin-top: 0;
    margin-left: auto;
    border-top: none;
    padding: 0;
  }

  .skeleton-logo-section {
    border-bottom: none;
    padding: 0;
  }

  .skeleton-stats-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 16px;
  }

  .skeleton-content-grid {
    padding: 16px;
    gap: 16px;
  }

  .skeleton-header {
    padding: 0 16px;
  }

  .skeleton-page-title {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .skeleton-title-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .skeleton-stats-row {
    grid-template-columns: 1fr;
  }

  .skeleton-header-right {
    gap: 4px;
  }

  .skeleton-header-button {
    width: 60px;
  }

  .skeleton-screenshots {
    flex-wrap: wrap;
  }

  .skeleton-screenshot {
    width: 60px;
    height: 45px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .skeleton-layout * {
    animation: none !important;
  }
}
